<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue'
import Create from './create.vue'
import Edit from './edit.vue'
import { enableOptions, lineTypeOptions, useLineSearchForm } from './schema'
import type { PageData } from '~/api/common/type'
import { productLineApi } from '~/api/line'
import type { ProductLine } from '~/api/line/types'
import PageContainer from '~/components/common/PageContainer.vue'

const open = ref({
  create: false,
  edit: false,
})
const editId = ref<string>()

const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})

const total = ref(0)
const loading = ref<boolean>(false)
const data = ref<ProductLine[]>([])
const selectedItems = ref<ProductLine[]>([])

// const confirm = useConfirm()
const searchForm = useLineSearchForm()

const search = searchForm.handleSubmit(async (values) => {
  try {
    loading.value = true
    const res = await productLineApi.page({
      pageData,
      searchParams: values,
    })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

// function openEditPage(id: string) {
//   editId.value = id
//   open.value.edit = true
// }

// function openCreatePage() {
//   open.value.create = true
// }

// function confirmDel(id: string, event: Event) {
//   confirm.require({
//     target: event.currentTarget as HTMLElement,
//     message: '确定要删除这条记录吗？',
//     icon: 'pi pi-exclamation-triangle',
//     rejectProps: {
//       label: '取消',
//       severity: 'secondary',
//       outlined: true,
//     },
//     acceptProps: {
//       label: '删除',
//       severity: 'danger',
//     },
//     accept: async () => {
//       try {
//         await productLineApi.delete(id)
//         success('删除成功')
//         search()
//       }
//       catch (error) {
//         console.error('删除失败:', error)
//       }
//     },
//   })
// }

// function confirmBatchDel(event: Event) {
//   if (!selectedItems.value.length) {
//     warn('请选择要删除的记录')
//     return
//   }

//   confirm.require({
//     target: event.currentTarget as HTMLElement,
//     message: `确定要删除选中的 ${selectedItems.value.length} 条记录吗？`,
//     icon: 'pi pi-exclamation-triangle',
//     rejectProps: {
//       label: '取消',
//       severity: 'secondary',
//       outlined: true,
//     },
//     acceptProps: {
//       label: '删除',
//       severity: 'danger',
//     },
//     accept: async () => {
//       try {
//         const ids = selectedItems.value.map(item => item.id!).filter(Boolean)
//         await productLineApi.batchDelete(ids)
//         success('批量删除成功')
//         selectedItems.value = []
//         search()
//       }
//       catch (error) {
//         console.error('批量删除失败:', error)
//       }
//     },
//   })
// }

// 获取线体类型显示文本
function getLineTypeLabel(type: string) {
  return lineTypeOptions.find(option => option.value === type)?.label || type
}

// 获取启用状态显示文本
function getEnableLabel(enable: boolean) {
  return enableOptions.find(option => option.value === enable)?.label || (enable ? '启用' : '禁用')
}

onMounted(() => {
  search()
})
</script>

<template>
  <PageContainer class="mt-4">
    <SearchBox :loading="loading" @submit="search" @search="search">
      <FInput name="name" label="线体名称" />
      <FInput name="code" label="线体编码" />
      <!-- <FSelect name="type" label="线体类型" :options="lineTypeOptions" />
      <FSelect name="enable" label="状态" :options="enableOptions" /> -->
    </SearchBox>
    <!-- <ButtonGroup class="pl-8">
      <Button outlined icon="pi pi-plus" @click="openCreatePage()" />
      <Button
        outlined
        severity="danger"
        icon="pi pi-trash"
        :disabled="!selectedItems.length"
        @click="confirmBatchDel($event)"
      />
    </ButtonGroup> -->
    <DataTable
      v-model:selection="selectedItems"
      class="p-4"
      :value="data"
      lazy paginator
      data-key="id"
      :rows="pageData.pageSize"
      :total-records="total"
      @page="page"
    >
      <Column selection-mode="multiple" :frozen="true" style="width: 3rem" />
      <Column field="name" header="线体名称" />
      <Column field="code" header="线体编码" />
      <Column header="线体类型">
        <template #body="slotProps">
          {{ getLineTypeLabel(slotProps.data.type) }}
        </template>
      </Column>
      <!-- <Column field="workshopName" header="车间" />
      <Column field="description" header="描述" /> -->
      <Column header="状态">
        <template #body="slotProps">
          <Tag :severity="slotProps.data.enable ? 'success' : 'danger'">
            {{ getEnableLabel(slotProps.data.enable) }}
          </Tag>
        </template>
      </Column>
      <!-- <Column header="创建时间">
        <template #body="slotProps">
          {{ slotProps.data.createTime ? formatDate(slotProps.data.createTime, 'YYYY-MM-DD HH:mm:ss') : '' }}
        </template>
      </Column> -->
      <!-- <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button outlined icon="pi pi-pencil" @click="openEditPage(slotProps.data.id)" />
            <Button outlined severity="danger" icon="pi pi-trash" @click="confirmDel(slotProps.data.id, $event)" />
          </div>
        </template>
      </Column> -->
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>
    <Create v-model:open="open.create" @save="search" />
    <Edit :id="editId" v-model:open="open.edit" @save="search" />
  </PageContainer>
</template>
