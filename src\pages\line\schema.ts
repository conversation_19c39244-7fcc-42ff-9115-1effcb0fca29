import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { z } from 'zod'
import type { ProductLineSearchParam } from '~/api/line/types'
import { LineType } from '~/api/line/types'

// 线体类型选项
export const lineTypeOptions = [
  {
    label: '单轨',
    value: LineType.singleTrack,
  },
  {
    label: '双轨',
    value: LineType.dualTrack,
  },
]

// 启用状态选项
export const enableOptions = [
  {
    label: '启用',
    value: true,
  },
  {
    label: '禁用',
    value: false,
  },
]

// 创建/编辑表单验证
const lineSchema = toTypedSchema(
  z.object({
    name: z.string().min(1, '线体名称不能为空'),
    code: z.string().min(1, '线体编码不能为空'),
    type: z.nativeEnum(LineType, { message: '请选择线体类型' }),
    workshopId: z.string().optional(),
    description: z.string().optional(),
    enable: z.boolean().default(true),
  }),
)

// 搜索表单验证
const lineSearchSchema = toTypedSchema(
  z.object({
    name: z.string().optional(),
    code: z.string().optional(),
    type: z.nativeEnum(LineType).optional(),
    workshopId: z.string().optional(),
    enable: z.boolean().optional(),
  }),
)

export function useLineForm() {
  const form = useForm({
    validationSchema: lineSchema,
  })
  return form
}

export function useLineSearchForm() {
  const form = useForm<ProductLineSearchParam>({
    validationSchema: lineSearchSchema,
  })
  return form
}
