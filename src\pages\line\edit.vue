<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import { enableOptions, lineTypeOptions, useLineForm } from './schema'
import { productLineApi } from '~/api/line'

const props = defineProps<{
  id?: string
}>()

const emits = defineEmits<{
  save: []
}>()

const loading = ref(false)
const open = defineModel<boolean>('open')

const { handleSubmit, setValues } = useLineForm()

const save = handleSubmit(async (values) => {
  if (!props.id) {
    error('缺少线体ID')
    return
  }

  try {
    loading.value = true
    await productLineApi.update(props.id, values)
    success('更新成功')
    emits('save')
    open.value = false
  }
  catch (error) {
    console.error('更新失败:', error)
  }
  finally {
    loading.value = false
  }
})

async function onShow() {
  if (!props.id) {
    error('缺少线体ID')
    return
  }

  try {
    loading.value = true
    const data = await productLineApi.get(props.id)
    setValues({
      name: data.name,
      code: data.code,
      type: data.type,
      workshopId: data.workshopId || '',
      description: data.description || '',
      enable: data.enable ?? true,
    })
  }
  catch (error) {
    console.error('获取线体信息失败:', error)
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="编辑线体" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInput name="name" label="线体名称" />
        <LInput name="code" label="线体编码" />
        <LSelect name="type" label="线体类型" :options="lineTypeOptions" />
        <LInput name="workshopId" label="车间ID" />
        <LInput name="description" label="描述" />
        <LSelect name="enable" label="状态" :options="enableOptions" />
      </FormLayout>
      <div class="mt-4 flex justify-end gap-4">
        <Button
          label="取消"
          severity="secondary"
          outlined
          @click="open = false"
        />
        <Button
          type="submit"
          label="保存"
          :loading="loading"
        />
      </div>
    </form>
  </Dialog>
</template>
