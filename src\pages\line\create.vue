<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import { enableOptions, lineTypeOptions, useLineForm } from './schema'
import { productLineApi } from '~/api/line'

const emits = defineEmits<{
  save: []
}>()

const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, handleSubmit } = useLineForm()

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await productLineApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  catch (error) {
    console.error('创建失败:', error)
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="新增线体" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInput name="name" label="线体名称" />
        <LInput name="code" label="线体编码" />
        <LSelect name="type" label="线体类型" :options="lineTypeOptions" />
        <LInput name="workshopId" label="车间ID" />
        <LInput name="description" label="描述" />
        <LSelect name="enable" label="状态" :options="enableOptions" />
      </FormLayout>
      <div class="mt-4 flex justify-end gap-4">
        <Button
          label="取消"
          severity="secondary"
          outlined
          @click="open = false"
        />
        <Button
          type="submit"
          label="保存"
          :loading="loading"
        />
      </div>
    </form>
  </Dialog>
</template>
