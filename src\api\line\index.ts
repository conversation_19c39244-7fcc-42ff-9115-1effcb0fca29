import type { PageList, Pageable } from '../common/type'
import type { DeviceInfo, DeviceSearchParam, ProductLine, ProductLineCreateParam, ProductLineSearchParam, ProductLineUpdateParam } from './types'
import { kyDelete, kyGet, kyPost, kyPut } from '~/utils/request'

export const productLineApi = {
  // 原有接口
  list: () => kyGet('line/list').json<ProductLine[]>(),
  listDevice: (param: DeviceSearchParam) => kyGet('device/list', param).json<DeviceInfo[]>(),
  listByWorkShopId: (workshopId: string) => kyGet(`line/listByWorkShopId/${workshopId}`).json<ProductLine[]>(),

  // CRUD接口
  create: (param: ProductLineCreateParam) => kyPost('line', param),
  page: (param: Pageable<Partial<ProductLineSearchParam>>) => kyPost('line/page', param).json<PageList<ProductLine>>(),
  get: (id: string) => kyGet(`line/${id}`).json<ProductLine>(),
  update: (id: string, param: ProductLineUpdateParam) => kyPut(`line/${id}`, param),
  delete: (id: string) => kyDelete(`line/${id}`),
  batchDelete: (ids: string[]) => kyDelete('line/batch', { json: ids }),
}
