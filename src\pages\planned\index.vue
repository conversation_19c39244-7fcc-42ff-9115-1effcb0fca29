<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue/datatable'
import { useConfirm } from 'primevue/useconfirm'
import Create from './create.vue'
import Edit from './edit.vue'
import Import from './import.vue'
import { usePlannedCapacitySearchForm } from './schema'
import type { PageData } from '~/api/common/type'
import type { PlannedCapacity } from '~/api/capacity/type'
import { capacityApi } from '~/api/capacity'

const loading = ref<boolean>(false)
const editId = ref<string>()
const data = ref<PlannedCapacity[]>([])
const total = ref(0)
const selectedItems = ref<PlannedCapacity[]>([])
const showButton = ref<boolean>(false)

const open = reactive({
  create: false,
  edit: false,
  import: false,
})

const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})

const searchForm = usePlannedCapacitySearchForm()
const confirm = useConfirm()

const search = searchForm.handleSubmit(async (searchParams) => {
  try {
    loading.value = true
    const res = await capacityApi.page({ searchParams, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

function openCreatePage() {
  open.create = true
}

function openEditPage(id: string) {
  open.edit = true
  editId.value = id
}

function handleImport() {
  open.import = true
}

async function handleExport() {
  loading.value = true
  try {
    const { valid, values: transformedValues } = await searchForm.validate()
    if (valid) {
      await handleFileExport(
        () => capacityApi.export(transformedValues),
        '标准产能数据.xlsx',
      )
    }
    else {
      console.warn('导出失败：表单数据不合法，请检查输入项。')
    }
  }
  catch (error) {
    console.error('导出过程中发生错误:', error)
  }
  finally {
    loading.value = false
  }
}

function confirmDel(id: string, event: any) {
  confirm.require({
    target: event.currentTarget,
    message: '确认删除？',
    group: 'delete',
    accept: async () => {
      await capacityApi.delete(id)
      success('删除成功')
      data.value = data.value.filter(o => o.id !== id)
    },
  })
}

function confirmBatchDel(event: any) {
  if (!selectedItems.value.length) {
    warn('请选择要删除的项目')
    return
  }

  confirm.require({
    target: event.currentTarget,
    message: `确认删除选中的 ${selectedItems.value.length} 项？`,
    group: 'delete',
    accept: async () => {
      try {
        loading.value = true
        await capacityApi.batchDelete(selectedItems.value.map(item => item.id))
        success('批量删除成功')
        selectedItems.value = []
        search()
      }
      finally {
        loading.value = false
      }
    },
  })
}

onMounted(() => {
  search()
})
</script>

<template>
  <PageContainer class="mt-4">
    <SearchBox :loading="loading" @submit="search" @search="search">
      <FDictSelect name="lineCode" label="线体编码" code="LINE_CODE" />
      <FInput name="productModel" label="产品型号" />
    </SearchBox>
    <ButtonGroup v-if="showButton" class="pl-8">
      <Button outlined icon="pi pi-plus" @click="openCreatePage()" />
      <Button outlined icon="pi pi-upload" @click="handleImport()" />
      <Button outlined icon="pi pi-download" @click="handleExport()" />
      <Button
        outlined
        severity="danger"
        icon="pi pi-trash"
        :disabled="!selectedItems.length"
        @click="confirmBatchDel($event)"
      />
    </ButtonGroup>
    <DataTable
      v-model:selection="selectedItems"
      class="p-4"
      :value="data"
      lazy paginator
      data-key="id"
      :rows="pageData.pageSize"
      :total-records="total"
      @page="page"
    >
      <Column v-if="showButton" selection-mode="multiple" :frozen="true" style="width: 3rem" />
      <Column field="lineCode" header="线体编码" />
      <Column field="productModel" header="产品型号" />
      <Column field="plannedQuantity" header="标准产能（pcs/h）" />
      <Column v-if="showButton" header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button outlined icon="pi pi-pencil" @click="openEditPage(slotProps.data.id)" />
            <Button outlined severity="danger" icon="pi pi-trash" @click="confirmDel(slotProps.data.id, $event)" />
          </div>
        </template>
      </Column>
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>
    <Create v-model:open="open.create" @save="search" />
    <Edit :id="editId" v-model:open="open.edit" @save="search" />
    <Import v-model:open="open.import" @refresh="search" />
  </PageContainer>
</template>
