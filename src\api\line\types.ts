export interface ProductLine {
  id?: string
  name: string
  code: string
  type: LineType
  workshopId?: string
  workshopName?: string
  description?: string
  enable?: boolean
  createTime?: Date
  updateTime?: Date
}

export interface ProductLineCreateParam {
  name: string
  code: string
  type: LineType
  workshopId?: string
  description?: string
  enable?: boolean
}

export interface ProductLineUpdateParam {
  name: string
  code: string
  type: LineType
  workshopId?: string
  description?: string
  enable?: boolean
}

export interface ProductLineSearchParam {
  name?: string
  code?: string
  type?: LineType
  workshopId?: string
  enable?: boolean
}

export enum LineType {
  singleTrack = 'SINGLE_TRACK',
  dualTrack = 'DUAL_TRACK',
}

export interface DeviceSearchParam {
  code: string
  startTime?: Date
  endTime?: Date
}

export enum DeviceType {
  PRINT_GKG = 'print_gkg',
  SMT_SAMSUNG = 'smt_samsung',
  SMT_NPM = 'smt_npm',
  SMT_NPM2 = 'smt_npm2',
  SMT_YAMAHA = 'smt_yamaha',
  AOI_JUZTE = 'aoi_juzte',
  AOI_YAMAHA = 'aoi_yamaha',
}

export interface DeviceInfo {
  name: string
  code: string
  ct: number
  type: DeviceType
}
