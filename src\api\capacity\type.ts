export interface CapacityReportDto {
  startWorkTime: string
  endWorkTime: string
  plannedQuantity: number
  actualQuantity: number
  achievementRate: string
  theoreticalQuantity: string
  status: '达标' | '未达标'
  reason: string | null
  capacityProductInfos: CapacityProductInfo[]
  stopTime: number
}

export interface CapacityProductInfo {
  productModel: string
  runTime: number
}

export interface LineSummary {
  lineCode: string
  totalPlannedQuantity: number
  totalActualQuantity: number
  totalTheoreticalQuality: number
  totalAchievementRate: number
  metCount: number
  notMetCount: number
  status: boolean
}

export interface PlannedCapacityInfo {
  track: string
  trackName: string
  lineCode: string
  productModel: string
  plannedQuantity: number
}

export interface CapacityReportWithLine {
  lineCode: string
  capacityReportDtos: CapacityReportDto[]
  lineSummary: LineSummary
  plannedCapacityInfos: PlannedCapacityInfo[]
}

export interface PlannedCapacity {
  id: string
  lineId: string
  workDate: string
  hourOfDay: number
  plannedQuantity: number
}

export interface PlannedCapacityCreate {
  lineCode: string
  productModel: string
  plannedQuantity: number
}

export interface PlannedCapacityEdit {
  lineCode: string
  productModel: string
  plannedQuantity: number
}

export interface PlannedCapacitySearch {
  lineCode?: string
  productModel?: string
}
